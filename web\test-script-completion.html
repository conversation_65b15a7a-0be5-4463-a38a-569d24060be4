<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脚本智能提示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #editor {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
        }
        .test-section {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a9e;
        }
        .info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>脚本智能提示测试</h1>
    
    <div class="info">
        <p>测试说明：</p>
        <ul>
            <li>点击"设置测试数据"按钮来模拟变量数据</li>
            <li>在编辑器中输入 <code>_data.</code> 应该能看到 EquipmentId 等字段</li>
            <li>输入 <code>_data.EquipmentId</code> 应该显示为 string 类型而不是 any</li>
        </ul>
    </div>

    <div class="test-section">
        <button onclick="setTestData()">设置测试数据</button>
        <button onclick="clearEditor()">清空编辑器</button>
    </div>

    <div id="editor"></div>

    <script src="https://unpkg.com/monaco-editor@0.34.1/min/vs/loader.js"></script>
    <script>
        let editor;
        
        require.config({ paths: { vs: 'https://unpkg.com/monaco-editor@0.34.1/min/vs' } });
        
        require(['vs/editor/editor.main'], function () {
            // 配置 Monaco Editor
            monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
                noSemanticValidation: false,
                noSyntaxValidation: false,
                diagnosticCodesToIgnore: [1108, 1005, 1003]
            });

            monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
                noSemanticValidation: false,
                noSyntaxValidation: false,
                diagnosticCodesToIgnore: [1108, 1005, 1003]
            });

            monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
                target: monaco.languages.typescript.ScriptTarget.ES2017,
                strict: false,
                allowNonTsExtensions: true,
                checkJs: true,
                allowJs: true,
            });

            // 创建编辑器
            editor = monaco.editor.create(document.getElementById('editor'), {
                value: '// 在这里输入代码，测试智能提示\n// 尝试输入 _data. 来查看智能提示\n// 输入 _data.EquipmentId 应该显示为 string 类型\n',
                language: 'typescript',
                theme: 'vs-dark',
                automaticLayout: true,
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                readOnly: false,
                cursorStyle: 'line',
                wordWrap: 'on',
                // 启用智能提示相关选项
                suggestOnTriggerCharacters: true,
                quickSuggestions: true,
                wordBasedSuggestions: 'off'
            });
        });

        // 模拟 scriptCompletion.ts 的核心功能
        function updateIntelliSenseData(data) {
            if (!monaco) return;

            const { localVariables = [], globalVariables = [] } = data;

            // 生成类型定义
            const generateTypeDefinitions = () => {
                const getTypeScriptType = (type) => {
                    switch (type.toLowerCase()) {
                        case 'string': return 'string';
                        case 'number':
                        case 'int':
                        case 'integer':
                        case 'float':
                        case 'double': return 'number';
                        case 'boolean':
                        case 'bool': return 'boolean';
                        case 'array': return 'any[]';
                        default: return 'any';
                    }
                };

                const dataProperties = [];

                // 处理局部变量
                if (localVariables.length > 0) {
                    dataProperties.push(...localVariables.map(v =>
                        `  ${v.key}: ${getTypeScriptType(v.type)};`
                    ));
                }

                // 处理全局变量
                if (globalVariables.length > 0) {
                    dataProperties.push(...globalVariables.map(v =>
                        `  ${v.key}: ${getTypeScriptType(v.type)};`
                    ));
                }

                const dataTypeDef = `interface DataObject {\n${dataProperties.join('\n')}\n}`;

                return [
                    dataTypeDef,
                    'declare const _data: DataObject;',
                    'declare const Utils: any;'
                ].join('\n\n');
            };

            const typeDefs = generateTypeDefinitions();
            console.log('生成的类型定义:', typeDefs);

            // 清除之前的类型定义
            try {
                monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
                monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
            } catch (error) {
                console.warn('清除旧类型定义失败:', error);
            }

            // 添加到Monaco Editor
            try {
                monaco.languages.typescript.typescriptDefaults.addExtraLib(
                    typeDefs,
                    'ts:custom-variables.d.ts'
                );
                monaco.languages.typescript.javascriptDefaults.addExtraLib(
                    typeDefs,
                    'ts:custom-variables.d.ts'
                );
                console.log('已更新Monaco Editor类型定义');

                // 强制刷新编辑器的类型检查
                if (editor) {
                    const model = editor.getModel();
                    if (model) {
                        monaco.languages.typescript.getTypeScriptWorker().then(worker => {
                            worker(model.uri).then(client => {
                                console.log('TypeScript worker 已更新');
                            });
                        });
                    }
                }
            } catch (error) {
                console.error('更新类型定义失败:', error);
            }
        }

        function setTestData() {
            const testData = {
                localVariables: [
                    {
                        id: '1',
                        key: 'EquipmentId',
                        type: 'string',
                        description: '设备ID'
                    },
                    {
                        id: '2',
                        key: 'EquipmentType',
                        type: 'string',
                        description: '设备类型'
                    },
                    {
                        id: '3',
                        key: 'Timestamp',
                        type: 'number',
                        description: '时间戳'
                    },
                    {
                        id: '4',
                        key: 'IsActive',
                        type: 'boolean',
                        description: '是否激活'
                    }
                ],
                globalVariables: [
                    {
                        id: '5',
                        key: 'SystemConfig',
                        type: 'object',
                        description: '系统配置'
                    }
                ]
            };
            
            updateIntelliSenseData(testData);
            alert('测试数据已设置！现在可以在编辑器中测试智能提示了。');
        }

        function clearEditor() {
            if (editor) {
                editor.setValue('// 在这里输入代码，测试智能提示\n// 尝试输入 _data. 来查看智能提示\n');
            }
        }
    </script>
</body>
</html>
