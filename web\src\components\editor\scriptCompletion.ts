import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 生成TypeScript类型定义
const generateTypeDefinitions = (variables: VariableData[], interfaceName: string): string => {
  const generateInterface = (vars: VariableData[], name: string): string => {
    const properties = vars
      .map((variable) => {
        let typeStr = '';

        // 根据类型生成TypeScript类型
        switch (variable.type.toLowerCase()) {
          case 'string':
            typeStr = 'string';
            break;
          case 'number':
          case 'int':
          case 'integer':
          case 'float':
          case 'double':
            typeStr = 'number';
            break;
          case 'boolean':
          case 'bool':
            typeStr = 'boolean';
            break;
          case 'array':
            typeStr = 'any[]';
            break;
          case 'object':
            if (variable.children && variable.children.length > 0) {
              // 生成嵌套接口
              const nestedInterfaceName = `${name}_${variable.key}`;
              const nestedInterface = generateInterface(variable.children, nestedInterfaceName);
              typeStr = nestedInterfaceName;
              return nestedInterface + `\n  ${variable.key}: ${typeStr};`;
            } else {
              typeStr = 'any';
            }
            break;
          default:
            if (variable.children && variable.children.length > 0) {
              // 如果有子属性，生成嵌套接口
              const nestedInterfaceName = `${name}_${variable.key}`;
              const nestedInterface = generateInterface(variable.children, nestedInterfaceName);
              typeStr = nestedInterfaceName;
              return nestedInterface + `\n  ${variable.key}: ${typeStr};`;
            } else {
              typeStr = 'any';
            }
        }

        return `  ${variable.key}: ${typeStr};`;
      })
      .join('\n');

    return `interface ${name} {\n${properties}\n}`;
  };

  return generateInterface(variables, interfaceName);
};

// 更新Monaco Editor的类型定义
const updateMonacoTypeDefinitions = () => {
  try {
    // 生成局部变量和全局变量的类型定义
    const localTypeDef = localVariables.length > 0 ? generateTypeDefinitions(localVariables, 'LocalVariables') : '';
    const globalTypeDef = globalVariables.length > 0 ? generateTypeDefinitions(globalVariables, 'GlobalVariables') : '';
    const currentTypeDef =
      currentVariables.length > 0 ? generateTypeDefinitions(currentVariables, 'CurrentVariables') : '';

    // 生成_data对象的类型定义
    let dataTypeDef = '';
    if (localVariables.length > 0 || globalVariables.length > 0) {
      const dataProperties = [];

      // 处理局部变量 - 简化处理，只使用基础类型
      if (localVariables.length > 0) {
        dataProperties.push(...localVariables.map((v) => `  ${v.key}: ${getTypeScriptType(v.type)};`));
      }

      // 处理全局变量 - 简化处理，只使用基础类型
      if (globalVariables.length > 0) {
        dataProperties.push(...globalVariables.map((v) => `  ${v.key}: ${getTypeScriptType(v.type)};`));
      }

      dataTypeDef = `interface DataObject {\n${dataProperties.join('\n')}\n}`;
    }

    // 组合所有类型定义
    const allTypeDefs = [
      localTypeDef,
      globalTypeDef,
      currentTypeDef,
      dataTypeDef,
      'declare const _data: DataObject;',
      'declare const Utils: any;',
    ]
      .filter((def) => def)
      .join('\n\n');

    // 清除之前的类型定义
    try {
      monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
      monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
    } catch (error) {
      console.warn('清除旧类型定义失败:', error);
    }

    // 添加到Monaco Editor
    if (allTypeDefs) {
      monaco.languages.typescript.typescriptDefaults.addExtraLib(allTypeDefs, 'ts:custom-variables.d.ts');
      monaco.languages.typescript.javascriptDefaults.addExtraLib(allTypeDefs, 'ts:custom-variables.d.ts');
      console.log('已更新Monaco Editor类型定义:', allTypeDefs);
    }
  } catch (error) {
    console.error('更新Monaco Editor类型定义失败:', error);
  }
};

// 获取TypeScript类型字符串
const getTypeScriptType = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'string':
      return 'string';
    case 'number':
    case 'int':
    case 'integer':
    case 'float':
    case 'double':
      return 'number';
    case 'boolean':
    case 'bool':
      return 'boolean';
    case 'array':
      return 'any[]';
    default:
      return 'any';
  }
};

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    currentVariables = data.currentVariables;
    console.log('更新当前变量:', currentVariables);
  }
  if (data.localVariables) {
    localVariables = data.localVariables;
    console.log('更新局部变量:', localVariables);
  }
  if (data.globalVariables) {
    globalVariables = data.globalVariables;
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
  }

  // 更新Monaco Editor的类型定义
  updateMonacoTypeDefinitions();
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 获取变量建议（展平所有层级）
const getVariableSuggestions = (variables: VariableData[], prefix: string = ''): any[] => {
  const flattened = flattenVariables(variables);

  return flattened.map(({ path, variable }) => {
    const displayPath = prefix ? `${prefix}${path}` : path;
    return {
      label: displayPath,
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: displayPath,
      detail: variable.type,
      documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
      sortText: `0_${displayPath}`,
    };
  });
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => ({
    label: variable.key,
    kind:
      variable.children && variable.children.length > 0
        ? monaco.languages.CompletionItemKind.Module
        : monaco.languages.CompletionItemKind.Variable,
    insertText: variable.key,
    detail: variable.type,
    documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
    sortText: `0_${variable.key}`,
  }));
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => ({
    label: func.label,
    kind: monaco.languages.CompletionItemKind.Function,
    insertText: func.script,
    detail: func.label,
    documentation: func.remark,
    sortText: `1_${func.label}`,
  }));
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): any[] => {
  return [
    {
      label: 'Utils',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'Utils.',
      detail: '工具类',
      documentation: '包含各种实用函数的工具类',
      sortText: '2_Utils',
    },
    {
      label: '_data',
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: '_data.',
      detail: '数据对象',
      documentation: '包含局部变量和全局变量的数据对象',
      sortText: '0__data',
    },
    {
      label: 'console.log',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'console.log($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '控制台输出',
      documentation: '在控制台输出信息',
      sortText: '3_console',
    },
    {
      label: 'JSON.parse',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.parse($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON解析',
      documentation: '解析JSON字符串',
      sortText: '3_JSON',
    },
    {
      label: 'JSON.stringify',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.stringify($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON序列化',
      documentation: '将对象序列化为JSON字符串',
      sortText: '3_JSON',
    },
  ];
};

// 查找嵌套变量的子属性
const findNestedVariable = (variables: VariableData[], path: string): VariableData | null => {
  const pathParts = path.split('.');
  console.log('查找嵌套变量:', { path, pathParts });

  for (const variable of variables) {
    if (variable.key === pathParts[0]) {
      if (pathParts.length === 1) {
        console.log('找到目标变量:', variable.key, '子属性数量:', variable.children?.length || 0);
        return variable;
      }
      // 递归查找子属性
      if (variable.children && variable.children.length > 0) {
        const remainingPath = pathParts.slice(1).join('.');
        return findNestedVariable(variable.children, remainingPath);
      }
    }
  }
  return null;
};

// 获取嵌套对象的子属性建议
const getNestedSuggestions = (variables: VariableData[], path: string): any[] => {
  const variable = findNestedVariable(variables, path);
  if (variable && variable.children && variable.children.length > 0) {
    console.log(
      '生成子属性建议:',
      variable.children.map((c) => c.key),
    );
    return variable.children.map((child) => ({
      label: child.key,
      kind:
        child.children && child.children.length > 0
          ? monaco.languages.CompletionItemKind.Module
          : monaco.languages.CompletionItemKind.Property,
      insertText: child.key,
      detail: child.type,
      documentation: child.description || child.pathDescription || `${child.type} 类型属性`,
      sortText: `0_${child.key}`,
    }));
  }
  return [];
};

// 检查变量的最终类型
const getFinalVariableType = (variables: VariableData[], path: string): string | null => {
  const variable = findNestedVariable(variables, path);
  return variable ? variable.type : null;
};

// 检查是否应该提供自定义建议
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  // 如果是通过点号触发的，检查是否是自定义对象
  if (triggerCharacter === '.') {
    // 如果是 _data. 相关的访问，总是提供自定义建议
    if (trimmedText.includes('_data.') || trimmedText.endsWith('_data.')) {
      return true;
    }

    // 如果是 Utils. 相关的访问，提供自定义建议
    if (trimmedText.includes('Utils.') || trimmedText.endsWith('Utils.')) {
      return true;
    }

    // 如果是我们自定义变量的访问（带点号），检查是否是自定义变量
    const customVariableMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
    if (customVariableMatch) {
      const varPath = customVariableMatch[1];

      // 检查是否是我们的自定义变量
      const isCustomVariable =
        currentVariables.some((v) => v.key === varPath.split('.')[0]) ||
        localVariables.some((v) => v.key === varPath.split('.')[0]) ||
        globalVariables.some((v) => v.key === varPath.split('.')[0]);

      if (isCustomVariable) {
        // 对于自定义变量，检查最终变量的类型
        const finalType = getFinalVariableType(currentVariables, varPath);

        // 如果最终类型是基础类型且没有子属性，让 JS 默认智能提示处理
        if (finalType && ['string', 'number', 'boolean'].includes(finalType.toLowerCase())) {
          const variable =
            findNestedVariable(currentVariables, varPath) ||
            findNestedVariable(localVariables, varPath) ||
            findNestedVariable(globalVariables, varPath);

          // 只有当变量没有子属性时才让 JS 默认处理
          if (!variable || !variable.children || variable.children.length === 0) {
            console.log('自定义变量最终类型是基础类型且无子属性:', finalType, '让 JS 默认智能提示处理');
            return false;
          }
        }
        return true;
      }
    }
    return false; // 其他点号触发的情况让默认智能提示处理
  }

  // 如果不是点号触发，总是提供自定义建议（包括变量名提示）
  return true;
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    console.log('智能提示触发:', {
      trimmedText,
      triggerCharacter,
      shouldProvideCustom: shouldProvideCustomSuggestions(trimmedText, triggerCharacter),
    });

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 检查是否是嵌套对象访问（如 _data.someObject.）
      const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);

      if (dataNestedMatch) {
        const nestedPath = dataNestedMatch[1];
        console.log('_data 嵌套路径:', nestedPath);

        // 在局部变量中查找
        const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
        suggestions.push(...localNestedSuggestions);

        // 在全局变量中查找
        const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
        suggestions.push(...globalNestedSuggestions);
      }
      // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
      else {
        const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);

        if (currentNestedMatch) {
          const nestedPath = currentNestedMatch[1];
          console.log('当前变量嵌套路径:', nestedPath);
          const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
          suggestions.push(...currentNestedSuggestions);
        }
      }

      // 如果没有找到嵌套建议，使用原有逻辑
      if (suggestions.length === 0) {
        // 如果是在 _data. 后面，提供局部和全局变量建议（只显示第一级）
        if (trimmedText.endsWith('_data.')) {
          // 添加局部变量建议（只显示第一级）
          const localSuggestions = getFirstLevelSuggestions(localVariables);
          suggestions.push(...localSuggestions);

          // 添加全局变量建议（只显示第一级）
          const globalSuggestions = getFirstLevelSuggestions(globalVariables);
          suggestions.push(...globalSuggestions);
        }
        // 如果是在 Utils. 后面，提供函数建议
        else if (trimmedText.endsWith('Utils.')) {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 否则提供自定义变量建议
        else {
          // 添加临时变量建议（不需要 _data 前缀）
          const currentSuggestions = getFirstLevelSuggestions(currentVariables);
          suggestions.push(...currentSuggestions);

          // 添加内置对象建议
          const builtinSuggestions = getBuiltinSuggestions();
          suggestions.push(...builtinSuggestions);

          // 添加函数建议
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
      }

      return {
        suggestions,
      };
    }

    // 对于其他情况，返回空建议，让默认的 TypeScript 智能提示处理
    return {
      suggestions: [],
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 检查是否是嵌套对象访问（如 _data.someObject.）
      const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
      if (dataNestedMatch) {
        const nestedPath = dataNestedMatch[1];
        // 在局部变量中查找
        const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
        suggestions.push(...localNestedSuggestions);

        // 在全局变量中查找
        const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
        suggestions.push(...globalNestedSuggestions);
      }
      // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
      else {
        const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
        if (currentNestedMatch) {
          const nestedPath = currentNestedMatch[1];
          const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
          suggestions.push(...currentNestedSuggestions);
        }
      }

      // 如果没有找到嵌套建议，使用原有逻辑
      if (suggestions.length === 0) {
        // 如果是在 _data. 后面，提供局部和全局变量建议（只显示第一级）
        if (trimmedText.endsWith('_data.')) {
          // 添加局部变量建议（只显示第一级）
          const localSuggestions = getFirstLevelSuggestions(localVariables);
          suggestions.push(...localSuggestions);

          // 添加全局变量建议（只显示第一级）
          const globalSuggestions = getFirstLevelSuggestions(globalVariables);
          suggestions.push(...globalSuggestions);
        }
        // 如果是在 Utils. 后面，提供函数建议
        else if (trimmedText.endsWith('Utils.')) {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 否则提供自定义变量建议
        else {
          // 添加临时变量建议（不需要 _data 前缀）
          const currentSuggestions = getFirstLevelSuggestions(currentVariables);
          suggestions.push(...currentSuggestions);

          // 添加内置对象建议
          const builtinSuggestions = getBuiltinSuggestions();
          suggestions.push(...builtinSuggestions);

          // 添加函数建议
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
      }

      return {
        suggestions,
      };
    }

    // 对于其他情况，返回空建议，让默认的 JavaScript 智能提示处理
    return {
      suggestions: [],
    };
  },
});
